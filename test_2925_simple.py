#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import imaplib
import poplib
import json


def test_2925_imap():
    """测试2925邮箱IMAP连接（标准端口143，无SSL）"""
    print("🔍 测试2925 IMAP连接...")
    
    try:
        # 使用标准IMAP端口143，无SSL
        connection = imaplib.IMAP4('imap.2925.com', 143)
        print("✅ IMAP连接建立成功")
        
        # 尝试登录
        result = connection.login('<EMAIL>', 'edward2wong')
        print(f"✅ IMAP登录成功: {result}")
        
        # 选择收件箱
        connection.select('INBOX')
        
        # 获取邮件数量
        result, messages = connection.search(None, 'ALL')
        if result == 'OK':
            email_ids = messages[0].split()
            print(f"📧 收件箱中有 {len(email_ids)} 封邮件")
        
        connection.logout()
        return True
        
    except Exception as e:
        print(f"❌ IMAP连接失败: {e}")
        return False


def test_2925_pop3():
    """测试2925邮箱POP3连接"""
    print("\n🔍 测试2925 POP3连接...")
    
    # 测试不同的POP3配置
    configs = [
        {'server': 'pop.2925.com', 'port': 110, 'ssl': False},
        {'server': 'pop.2925.com', 'port': 995, 'ssl': True},
        {'server': 'mail.2925.com', 'port': 110, 'ssl': False},
        {'server': 'mail.2925.com', 'port': 995, 'ssl': True},
    ]
    
    for config in configs:
        try:
            print(f"  尝试: {config['server']}:{config['port']} (SSL: {config['ssl']})")
            
            if config['ssl']:
                connection = poplib.POP3_SSL(config['server'], config['port'])
            else:
                connection = poplib.POP3(config['server'], config['port'])
            
            print("    ✅ POP3连接建立成功")
            
            # 尝试登录
            connection.user('<EMAIL>')
            connection.pass_('edward2wong')
            
            print("    ✅ POP3登录成功")
            
            # 获取邮件统计
            num_messages, total_size = connection.stat()
            print(f"    📧 邮箱统计: {num_messages} 封邮件, 总大小: {total_size} 字节")
            
            connection.quit()
            return True
            
        except Exception as e:
            print(f"    ❌ 失败: {e}")
            continue
    
    return False


def test_credentials():
    """测试不同的用户名密码组合"""
    print("\n🔍 测试不同的认证信息...")
    
    # 可能的用户名格式
    usernames = [
        '<EMAIL>',
        'edward_wong',
        'edwardwonggang',  # GitHub用户名
    ]
    
    # 可能的密码
    passwords = [
        'edward2wong',
        'edward_wong',
        'Edward2wong',
        'EDWARD2WONG',
    ]
    
    for username in usernames:
        for password in passwords:
            try:
                print(f"  尝试: {username} / {password}")
                
                connection = imaplib.IMAP4('imap.2925.com', 143)
                result = connection.login(username, password)
                
                print(f"    ✅ 成功: {username} / {password}")
                connection.logout()
                return username, password
                
            except Exception as e:
                print(f"    ❌ 失败: {e}")
                continue
    
    return None, None


def main():
    """主测试函数"""
    print("🚀 开始测试2925邮箱连接")
    print("=" * 50)
    
    # 测试1: 标准IMAP配置
    if test_2925_imap():
        print("🎉 IMAP连接成功！")
        return
    
    # 测试2: POP3配置
    if test_2925_pop3():
        print("🎉 POP3连接成功！")
        return
    
    # 测试3: 不同的认证信息
    username, password = test_credentials()
    if username and password:
        print(f"🎉 找到正确的认证信息: {username} / {password}")
        
        # 更新配置文件
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        config['email']['address'] = username if '@' in username else f"{username}@2925.com"
        config['email']['password'] = password
        
        with open('config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print("✅ 配置文件已更新")
        return
    
    print("\n❌ 所有测试都失败了")
    print("\n🔧 建议检查:")
    print("  1. 邮箱地址是否正确")
    print("  2. 密码是否正确")
    print("  3. 是否需要在2925网站上启用邮件客户端访问")
    print("  4. 是否需要生成应用专用密码")


if __name__ == "__main__":
    main()
