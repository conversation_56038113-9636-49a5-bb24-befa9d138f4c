import imaplib
import email
import json
import re
import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional


class EmailReader:
    def __init__(self, config_file: str = "config.json"):
        """初始化邮件读取器"""
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        self.email_config = config['email']
        self.imap_server = self.email_config['imap_server']
        self.imap_port = self.email_config['imap_port']
        self.email_address = self.email_config['address']
        self.password = self.email_config['password']
        self.use_ssl = self.email_config['use_ssl']
        
        self.connection = None
    
    def connect(self) -> bool:
        """连接到IMAP服务器"""
        try:
            if self.use_ssl:
                self.connection = imaplib.IMAP4_SSL(self.imap_server, self.imap_port)
            else:
                self.connection = imaplib.IMAP4(self.imap_server, self.imap_port)
            
            # 登录
            result = self.connection.login(self.email_address, self.password)
            print(f"IMAP连接成功: {result}")
            return True
            
        except Exception as e:
            print(f"IMAP连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开IMAP连接"""
        if self.connection:
            try:
                self.connection.close()
                self.connection.logout()
                print("IMAP连接已断开")
            except:
                pass
    
    def get_recent_emails(self, folder: str = "INBOX", limit: int = 10) -> List[Dict]:
        """获取最近的邮件"""
        if not self.connection:
            if not self.connect():
                return []
        
        try:
            # 选择邮箱文件夹
            self.connection.select(folder)
            
            # 搜索最近的邮件
            result, messages = self.connection.search(None, 'ALL')
            
            if result != 'OK':
                print("搜索邮件失败")
                return []
            
            # 获取邮件ID列表
            email_ids = messages[0].split()
            
            # 获取最近的邮件（倒序）
            recent_ids = email_ids[-limit:] if len(email_ids) >= limit else email_ids
            recent_ids.reverse()  # 最新的在前
            
            emails = []
            for email_id in recent_ids:
                email_data = self._fetch_email(email_id)
                if email_data:
                    emails.append(email_data)
            
            return emails
            
        except Exception as e:
            print(f"获取邮件失败: {e}")
            return []
    
    def _fetch_email(self, email_id: bytes) -> Optional[Dict]:
        """获取单个邮件的详细信息"""
        try:
            result, msg_data = self.connection.fetch(email_id, '(RFC822)')
            
            if result != 'OK':
                return None
            
            # 解析邮件
            email_message = email.message_from_bytes(msg_data[0][1])
            
            # 提取邮件信息
            subject = self._decode_header(email_message['Subject'])
            sender = self._decode_header(email_message['From'])
            date = email_message['Date']
            
            # 获取邮件正文
            body = self._get_email_body(email_message)
            
            return {
                'id': email_id.decode(),
                'subject': subject,
                'sender': sender,
                'date': date,
                'body': body
            }
            
        except Exception as e:
            print(f"解析邮件失败: {e}")
            return None
    
    def _decode_header(self, header: str) -> str:
        """解码邮件头部信息"""
        if not header:
            return ""
        
        try:
            decoded_parts = email.header.decode_header(header)
            decoded_header = ""
            
            for part, encoding in decoded_parts:
                if isinstance(part, bytes):
                    if encoding:
                        decoded_header += part.decode(encoding)
                    else:
                        decoded_header += part.decode('utf-8', errors='ignore')
                else:
                    decoded_header += part
            
            return decoded_header
        except:
            return str(header)
    
    def _get_email_body(self, email_message) -> str:
        """提取邮件正文"""
        body = ""
        
        if email_message.is_multipart():
            for part in email_message.walk():
                content_type = part.get_content_type()
                content_disposition = str(part.get("Content-Disposition"))
                
                # 跳过附件
                if "attachment" in content_disposition:
                    continue
                
                if content_type == "text/plain":
                    charset = part.get_content_charset() or 'utf-8'
                    body = part.get_payload(decode=True).decode(charset, errors='ignore')
                    break
                elif content_type == "text/html" and not body:
                    charset = part.get_content_charset() or 'utf-8'
                    body = part.get_payload(decode=True).decode(charset, errors='ignore')
        else:
            charset = email_message.get_content_charset() or 'utf-8'
            body = email_message.get_payload(decode=True).decode(charset, errors='ignore')
        
        return body
    
    def extract_verification_code(self, email_body: str) -> Optional[str]:
        """从邮件正文中提取验证码"""
        # 常见的验证码模式
        patterns = [
            r'验证码[：:]\s*([A-Za-z0-9]{4,8})',
            r'verification code[：:]\s*([A-Za-z0-9]{4,8})',
            r'code[：:]\s*([A-Za-z0-9]{4,8})',
            r'([A-Za-z0-9]{6})',  # 6位数字或字母
            r'([0-9]{4,8})',      # 4-8位数字
        ]
        
        for pattern in patterns:
            match = re.search(pattern, email_body, re.IGNORECASE)
            if match:
                return match.group(1)
        
        return None
    
    def wait_for_verification_email(self, sender_keyword: str = "augment", 
                                  timeout: int = 300, check_interval: int = 10) -> Optional[str]:
        """等待验证邮件并提取验证码"""
        print(f"开始等待来自 '{sender_keyword}' 的验证邮件...")
        
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            emails = self.get_recent_emails(limit=5)
            
            for email_data in emails:
                # 检查发件人是否包含关键词
                if sender_keyword.lower() in email_data['sender'].lower():
                    print(f"找到验证邮件: {email_data['subject']}")
                    
                    # 提取验证码
                    verification_code = self.extract_verification_code(email_data['body'])
                    if verification_code:
                        print(f"提取到验证码: {verification_code}")
                        return verification_code
                    else:
                        print("未能从邮件中提取验证码")
                        print(f"邮件内容: {email_data['body'][:200]}...")
            
            print(f"等待中... ({int(time.time() - start_time)}s/{timeout}s)")
            time.sleep(check_interval)
        
        print("等待验证邮件超时")
        return None


def main():
    """测试邮件读取功能"""
    reader = EmailReader()
    
    if reader.connect():
        print("=== 获取最近的邮件 ===")
        emails = reader.get_recent_emails(limit=3)
        
        for i, email_data in enumerate(emails, 1):
            print(f"\n邮件 {i}:")
            print(f"主题: {email_data['subject']}")
            print(f"发件人: {email_data['sender']}")
            print(f"日期: {email_data['date']}")
            print(f"正文预览: {email_data['body'][:100]}...")
            
            # 尝试提取验证码
            code = reader.extract_verification_code(email_data['body'])
            if code:
                print(f"检测到验证码: {code}")
        
        reader.disconnect()
    else:
        print("无法连接到邮箱服务器")


if __name__ == "__main__":
    main()
