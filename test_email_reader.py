#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from email_reader import EmailReader
import time


def test_basic_email_reading():
    """测试基本的邮件读取功能"""
    print("=== 测试基本邮件读取功能 ===")
    
    reader = EmailReader()
    
    if not reader.connect():
        print("❌ 连接失败")
        return False
    
    print("✅ 连接成功")
    
    # 获取最近的邮件
    emails = reader.get_recent_emails(limit=5)
    
    if not emails:
        print("📭 没有找到邮件")
    else:
        print(f"📧 找到 {len(emails)} 封邮件")
        
        for i, email_data in enumerate(emails, 1):
            print(f"\n--- 邮件 {i} ---")
            print(f"主题: {email_data['subject']}")
            print(f"发件人: {email_data['sender']}")
            print(f"日期: {email_data['date']}")
            
            # 检查是否包含验证码
            code = reader.extract_verification_code(email_data['body'])
            if code:
                print(f"🔑 检测到验证码: {code}")
            
            # 显示邮件正文预览
            body_preview = email_data['body'][:200].replace('\n', ' ').strip()
            print(f"正文预览: {body_preview}...")
    
    reader.disconnect()
    return True


def test_wait_for_verification():
    """测试等待验证邮件功能"""
    print("\n=== 测试等待验证邮件功能 ===")
    print("这个测试会等待30秒来检测新的验证邮件")
    print("你可以在测试期间发送一封包含验证码的邮件到你的邮箱")
    
    reader = EmailReader()
    
    # 等待验证邮件（较短的超时时间用于测试）
    verification_code = reader.wait_for_verification_email(
        sender_keyword="test",  # 可以修改为你想测试的发件人关键词
        timeout=30,  # 30秒超时
        check_interval=5  # 每5秒检查一次
    )
    
    if verification_code:
        print(f"✅ 成功获取验证码: {verification_code}")
    else:
        print("❌ 未能获取验证码")
    
    reader.disconnect()


def test_verification_code_extraction():
    """测试验证码提取功能"""
    print("\n=== 测试验证码提取功能 ===")
    
    reader = EmailReader()
    
    # 测试不同格式的验证码文本
    test_texts = [
        "您的验证码是: 123456",
        "Your verification code: ABC123",
        "验证码：789012",
        "Please use this code: XYZ789",
        "Your 6-digit code is 456789",
        "验证码为 654321，请在10分钟内使用",
        "Code: 999888"
    ]
    
    for text in test_texts:
        code = reader.extract_verification_code(text)
        print(f"文本: {text}")
        print(f"提取的验证码: {code}")
        print("-" * 50)


if __name__ == "__main__":
    print("🚀 开始测试邮件读取功能")
    
    # 测试1: 基本邮件读取
    if test_basic_email_reading():
        print("\n" + "="*50)
        
        # 测试2: 验证码提取
        test_verification_code_extraction()
        
        print("\n" + "="*50)
        
        # 测试3: 等待验证邮件（可选）
        user_input = input("\n是否要测试等待验证邮件功能？(y/n): ").lower().strip()
        if user_input == 'y':
            test_wait_for_verification()
    
    print("\n🎉 测试完成")
