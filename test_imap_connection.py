#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import imaplib
import json
import socket


def test_imap_connection(server, port, email, password, use_ssl=True):
    """测试IMAP连接"""
    print(f"\n🔍 测试连接: {server}:{port} (SSL: {use_ssl})")
    
    try:
        # 首先测试端口是否开放
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        result = sock.connect_ex((server, port))
        sock.close()
        
        if result != 0:
            print(f"❌ 端口 {port} 无法连接")
            return False
        else:
            print(f"✅ 端口 {port} 可以连接")
        
        # 尝试IMAP连接
        if use_ssl:
            connection = imaplib.IMAP4_SSL(server, port)
        else:
            connection = imaplib.IMAP4(server, port)
        
        print("✅ IMAP连接建立成功")
        
        # 尝试登录
        result = connection.login(email, password)
        print(f"✅ 登录成功: {result}")
        
        # 获取邮箱列表
        result, mailboxes = connection.list()
        if result == 'OK':
            print(f"✅ 邮箱列表获取成功，共 {len(mailboxes)} 个邮箱")
        
        connection.logout()
        return True
        
    except imaplib.IMAP4.error as e:
        print(f"❌ IMAP错误: {e}")
        return False
    except socket.gaierror as e:
        print(f"❌ DNS解析错误: {e}")
        return False
    except socket.timeout as e:
        print(f"❌ 连接超时: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False


def main():
    """测试不同的IMAP服务器配置"""
    print("🚀 开始测试2925邮箱IMAP连接")
    
    # 读取配置
    with open('config.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    email = config['email']['address']
    password = config['email']['password']
    
    # 测试配置列表
    test_configs = [
        # 当前配置
        {
            'name': '当前配置',
            'server': config['email']['imap_server'],
            'port': config['email']['imap_port'],
            'ssl': config['email']['use_ssl']
        }
    ]
    
    # 添加备选配置
    if 'alternative_servers' in config:
        for alt_config in config['alternative_servers']:
            test_configs.append({
                'name': alt_config['name'],
                'server': alt_config['imap_server'],
                'port': alt_config['imap_port'],
                'ssl': alt_config['use_ssl']
            })
    
    # 添加更多常见配置
    common_configs = [
        {'name': 'POP3端口995', 'server': 'mail.2925.com', 'port': 995, 'ssl': True},
        {'name': 'POP3端口110', 'server': 'mail.2925.com', 'port': 110, 'ssl': False},
        {'name': 'IMAP端口143', 'server': 'imap.2925.com', 'port': 143, 'ssl': False},
        {'name': 'SMTP端口587', 'server': 'smtp.2925.com', 'port': 587, 'ssl': False},
        {'name': 'SMTP端口465', 'server': 'smtp.2925.com', 'port': 465, 'ssl': True},
    ]
    
    test_configs.extend(common_configs)
    
    print(f"📧 测试邮箱: {email}")
    print("=" * 60)
    
    success_configs = []
    
    for config_info in test_configs:
        try:
            if test_imap_connection(
                config_info['server'], 
                config_info['port'], 
                email, 
                password, 
                config_info['ssl']
            ):
                success_configs.append(config_info)
                print(f"🎉 {config_info['name']} 连接成功！")
            else:
                print(f"💥 {config_info['name']} 连接失败")
        except KeyboardInterrupt:
            print("\n⏹️ 用户中断测试")
            break
        except Exception as e:
            print(f"💥 {config_info['name']} 测试异常: {e}")
        
        print("-" * 60)
    
    # 总结
    print("\n📊 测试总结:")
    if success_configs:
        print("✅ 成功的配置:")
        for config_info in success_configs:
            print(f"  - {config_info['name']}: {config_info['server']}:{config_info['port']} (SSL: {config_info['ssl']})")
    else:
        print("❌ 没有找到可用的配置")
        print("\n🔧 建议检查:")
        print("  1. 邮箱地址和密码是否正确")
        print("  2. 2925邮箱是否支持IMAP/POP3协议")
        print("  3. 是否需要在邮箱设置中启用IMAP访问")
        print("  4. 网络连接是否正常")


if __name__ == "__main__":
    main()
