#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import imaplib
import poplib
import smtplib
import socket
import ssl
import base64
import json


def test_smtp_auth():
    """测试SMTP认证，有时SMTP认证成功说明账号密码正确"""
    print("🔍 测试SMTP认证...")

    smtp_configs = [
        {'server': 'smtp.2925.com', 'port': 25, 'ssl': False, 'starttls': False},
        {'server': 'smtp.2925.com', 'port': 587, 'ssl': False, 'starttls': True},
        {'server': 'smtp.2925.com', 'port': 465, 'ssl': True, 'starttls': False},
        {'server': 'mail.2925.com', 'port': 25, 'ssl': False, 'starttls': False},
        {'server': 'mail.2925.com', 'port': 587, 'ssl': False, 'starttls': True},
        {'server': 'mail.2925.com', 'port': 465, 'ssl': True, 'starttls': False},
    ]

    for config in smtp_configs:
        try:
            print(f"  尝试SMTP: {config['server']}:{config['port']} (SSL: {config['ssl']}, STARTTLS: {config['starttls']})")

            if config['ssl']:
                server = smtplib.SMTP_SSL(config['server'], config['port'])
            else:
                server = smtplib.SMTP(config['server'], config['port'])

            print("    ✅ SMTP连接建立成功")

            if config['starttls'] and not config['ssl']:
                server.starttls()
                print("    ✅ STARTTLS成功")

            # 尝试登录
            server.login('<EMAIL>', 'edward2wong')
            print("    🎉 SMTP登录成功！这说明账号密码是正确的")
            server.quit()
            return True

        except Exception as e:
            print(f"    ❌ 失败: {e}")
            continue

    return False


def test_raw_imap_commands():
    """使用原始IMAP命令测试"""
    print("\n🔍 测试原始IMAP命令...")

    try:
        # 连接到IMAP服务器
        connection = imaplib.IMAP4('imap.2925.com', 143)
        print("✅ IMAP连接建立成功")

        # 获取服务器能力
        capabilities = connection.capabilities
        print(f"📋 服务器能力: {capabilities}")

        # 尝试不同的认证方法
        auth_methods = ['LOGIN', 'PLAIN', 'CRAM-MD5']

        for method in auth_methods:
            if method.encode() in capabilities:
                print(f"🔐 尝试认证方法: {method}")
                try:
                    if method == 'LOGIN':
                        result = connection.login('<EMAIL>', 'edward2wong')
                        print(f"    ✅ {method} 成功: {result}")
                        connection.logout()
                        return True
                    elif method == 'PLAIN':
                        # PLAIN认证
                        auth_string = f'\<EMAIL>\0edward2wong'
                        auth_string_b64 = base64.b64encode(auth_string.encode()).decode()
                        result = connection.authenticate('PLAIN', lambda x: auth_string_b64)
                        print(f"    ✅ {method} 成功: {result}")
                        connection.logout()
                        return True
                except Exception as e:
                    print(f"    ❌ {method} 失败: {e}")
            else:
                print(f"❌ 服务器不支持 {method} 认证")

        connection.logout()

    except Exception as e:
        print(f"❌ 原始IMAP测试失败: {e}")

    return False


def test_telnet_imap():
    """使用telnet方式测试IMAP"""
    print("\n🔍 使用原始socket测试IMAP...")

    try:
        # 创建socket连接
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        sock.connect(('imap.2925.com', 143))

        # 接收欢迎消息
        welcome = sock.recv(1024).decode()
        print(f"📨 服务器欢迎消息: {welcome.strip()}")

        # 发送CAPABILITY命令
        sock.send(b'A001 CAPABILITY\r\n')
        capability_response = sock.recv(1024).decode()
        print(f"📋 服务器能力: {capability_response.strip()}")

        # 尝试登录
        login_cmd = f'A002 LOGIN <EMAIL> edward22wong\r\n'
        sock.send(login_cmd.encode())
        login_response = sock.recv(1024).decode()
        print(f"🔐 登录响应: {login_response.strip()}")

        if 'OK' in login_response:
            print("🎉 原始socket登录成功！")
            sock.send(b'A003 LOGOUT\r\n')
            sock.recv(1024)
            sock.close()
            return True
        else:
            print("❌ 原始socket登录失败")

        sock.close()

    except Exception as e:
        print(f"❌ 原始socket测试失败: {e}")

    return False


def check_2925_web_settings():
    """检查2925邮箱网页设置"""
    print("\n🔍 检查2925邮箱设置建议...")

    print("📝 请检查以下设置:")
    print("  1. 登录 https://2925.com 或相关管理页面")
    print("  2. 查找 '设置' -> '邮件客户端' 或 'POP3/IMAP设置'")
    print("  3. 确认是否需要:")
    print("     - 启用POP3/IMAP访问")
    print("     - 生成应用专用密码")
    print("     - 允许不安全的应用访问")
    print("     - 设置特定的认证方式")

    print("\n🔧 常见的邮箱服务商要求:")
    print("  - Gmail: 需要应用专用密码")
    print("  - Outlook: 需要启用IMAP")
    print("  - QQ邮箱: 需要生成授权码")
    print("  - 163邮箱: 需要开启客户端授权密码")


def test_alternative_ports():
    """测试其他可能的端口"""
    print("\n🔍 测试其他可能的端口...")

    # 一些邮箱服务商使用的非标准端口
    test_ports = [
        {'proto': 'IMAP', 'server': 'imap.2925.com', 'ports': [143, 993, 585, 993]},
        {'proto': 'POP3', 'server': 'pop.2925.com', 'ports': [110, 995, 1110]},
        {'proto': 'IMAP', 'server': 'mail.2925.com', 'ports': [143, 993, 2143]},
    ]

    for test_config in test_ports:
        for port in test_config['ports']:
            try:
                print(f"  测试 {test_config['proto']} {test_config['server']}:{port}")

                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(5)
                result = sock.connect_ex((test_config['server'], port))
                sock.close()

                if result == 0:
                    print(f"    ✅ 端口 {port} 开放")
                else:
                    print(f"    ❌ 端口 {port} 关闭")

            except Exception as e:
                print(f"    ❌ 端口 {port} 测试失败: {e}")


def main():
    """主诊断函数"""
    print("🚀 开始2925邮箱深度诊断")
    print("=" * 60)

    # 测试1: SMTP认证（通常更容易成功）
    if test_smtp_auth():
        print("✅ SMTP认证成功，说明账号密码正确")
        print("问题可能在于IMAP/POP3的特殊设置要求")
    else:
        print("❌ SMTP认证也失败，可能需要检查账号设置")

    # 测试2: 原始IMAP命令
    if test_raw_imap_commands():
        print("✅ 原始IMAP命令成功")
        return

    # 测试3: 原始socket测试
    if test_telnet_imap():
        print("✅ 原始socket测试成功")
        return

    # 测试4: 其他端口
    test_alternative_ports()

    # 测试5: 设置建议
    check_2925_web_settings()

    print("\n📊 诊断总结:")
    print("❌ 所有自动化测试都失败了")
    print("\n💡 建议的解决步骤:")
    print("  1. 🌐 登录2925邮箱网页版，确认账号正常")
    print("  2. ⚙️  查找邮箱设置中的POP3/IMAP选项")
    print("  3. 🔑 查看是否需要生成应用专用密码")
    print("  4. 📞 联系2925客服确认邮件客户端设置")
    print("  5. 🔄 考虑使用网页版邮箱API（如果有的话）")


if __name__ == "__main__":
    main()
