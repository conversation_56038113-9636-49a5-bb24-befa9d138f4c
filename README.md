# 2925邮箱自动化工具

这个项目实现了通过IMAP协议自动读取2925无限邮箱内容，并提取验证码的功能。

## 功能特性

- ✅ 通过IMAP协议连接2925邮箱
- ✅ 读取最近的邮件内容
- ✅ 自动提取验证码（支持多种格式）
- ✅ 等待验证邮件到达并自动提取验证码
- ✅ 支持中英文验证码格式

## 文件结构

```
├── config.json          # 邮箱配置文件
├── email_reader.py      # 邮件读取核心模块
├── test_email_reader.py # 测试脚本
├── requirements.txt     # 依赖包列表
└── README.md           # 说明文档
```

## 配置说明

### config.json
```json
{
  "email": {
    "address": "<EMAIL>",
    "password": "edward2wong",
    "imap_server": "imap.2925.com",
    "imap_port": 993,
    "use_ssl": true
  }
}
```

**注意**: 如果2925的IMAP服务器地址不是 `imap.2925.com`，请根据实际情况修改。

## 使用方法

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 基本使用
```python
from email_reader import EmailReader

# 创建邮件读取器
reader = EmailReader()

# 连接邮箱
if reader.connect():
    # 获取最近5封邮件
    emails = reader.get_recent_emails(limit=5)
    
    for email_data in emails:
        print(f"主题: {email_data['subject']}")
        print(f"发件人: {email_data['sender']}")
        
        # 尝试提取验证码
        code = reader.extract_verification_code(email_data['body'])
        if code:
            print(f"验证码: {code}")
    
    reader.disconnect()
```

### 3. 等待验证邮件
```python
from email_reader import EmailReader

reader = EmailReader()

# 等待来自Augment的验证邮件
verification_code = reader.wait_for_verification_email(
    sender_keyword="augment",  # 发件人关键词
    timeout=300,              # 等待5分钟
    check_interval=10         # 每10秒检查一次
)

if verification_code:
    print(f"获取到验证码: {verification_code}")
else:
    print("未能获取验证码")
```

## 测试

运行测试脚本：
```bash
python test_email_reader.py
```

测试包括：
1. 基本邮件读取功能
2. 验证码提取功能
3. 等待验证邮件功能

## 支持的验证码格式

- `验证码: 123456`
- `verification code: ABC123`
- `验证码：789012`
- `Code: XYZ789`
- `Your 6-digit code is 456789`
- 纯数字验证码（4-8位）
- 字母数字混合验证码（4-8位）

## API参考

### EmailReader类

#### 方法

- `connect()`: 连接到IMAP服务器
- `disconnect()`: 断开连接
- `get_recent_emails(folder="INBOX", limit=10)`: 获取最近的邮件
- `extract_verification_code(email_body)`: 从邮件正文提取验证码
- `wait_for_verification_email(sender_keyword, timeout=300, check_interval=10)`: 等待验证邮件

## 注意事项

1. 确保2925邮箱支持IMAP协议
2. 检查IMAP服务器地址和端口是否正确
3. 如果连接失败，可能需要在2925邮箱设置中启用IMAP访问
4. 验证码提取基于正则表达式，可能需要根据实际邮件格式调整

## 下一步计划

- [ ] 集成网页自动化功能
- [ ] 实现完整的Augment注册流程
- [ ] 添加更多邮件服务商支持
- [ ] 优化验证码识别准确率
