#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import socket
import json
import re
import time
import email
import base64
from typing import List, Dict, Optional


class SocketEmailReader:
    def __init__(self, config_file: str = "config.json"):
        """初始化基于socket的邮件读取器"""
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        self.email_config = config['email']
        self.imap_server = 'imap.2925.com'  # 使用测试成功的服务器
        self.imap_port = 143
        self.email_address = self.email_config['address']
        self.password = self.email_config['password']
        
        self.socket = None
        self.command_id = 1000
    
    def _get_next_command_id(self) -> str:
        """获取下一个命令ID"""
        self.command_id += 1
        return f"A{self.command_id:03d}"
    
    def _send_command(self, command: str) -> str:
        """发送IMAP命令并接收响应"""
        cmd_id = self._get_next_command_id()
        full_command = f"{cmd_id} {command}\r\n"
        
        print(f"📤 发送: {full_command.strip()}")
        self.socket.send(full_command.encode())
        
        # 接收响应
        response = ""
        while True:
            data = self.socket.recv(4096).decode('utf-8', errors='ignore')
            response += data
            
            # 检查是否收到完整响应
            if f"{cmd_id} OK" in response or f"{cmd_id} NO" in response or f"{cmd_id} BAD" in response:
                break
            
            # 防止无限等待
            if len(response) > 100000:  # 100KB限制
                break
        
        print(f"📥 接收: {response.strip()}")
        return response
    
    def connect(self) -> bool:
        """连接到IMAP服务器"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(30)
            self.socket.connect((self.imap_server, self.imap_port))
            
            # 接收欢迎消息
            welcome = self.socket.recv(1024).decode()
            print(f"📨 服务器欢迎: {welcome.strip()}")
            
            # 登录
            login_response = self._send_command(f"LOGIN {self.email_address} {self.password}")
            
            if "OK" in login_response:
                print("✅ 登录成功")
                return True
            else:
                print(f"❌ 登录失败: {login_response}")
                return False
                
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开连接"""
        if self.socket:
            try:
                self._send_command("LOGOUT")
                self.socket.close()
                print("✅ 连接已断开")
            except:
                pass
    
    def select_mailbox(self, mailbox: str = "INBOX") -> bool:
        """选择邮箱"""
        try:
            response = self._send_command(f"SELECT {mailbox}")
            if "OK" in response:
                print(f"✅ 已选择邮箱: {mailbox}")
                return True
            else:
                print(f"❌ 选择邮箱失败: {response}")
                return False
        except Exception as e:
            print(f"❌ 选择邮箱异常: {e}")
            return False
    
    def get_email_count(self) -> int:
        """获取邮件数量"""
        try:
            response = self._send_command("STATUS INBOX (MESSAGES)")
            
            # 解析邮件数量
            match = re.search(r'MESSAGES (\d+)', response)
            if match:
                count = int(match.group(1))
                print(f"📧 邮箱中有 {count} 封邮件")
                return count
            else:
                print("❌ 无法解析邮件数量")
                return 0
                
        except Exception as e:
            print(f"❌ 获取邮件数量失败: {e}")
            return 0
    
    def search_emails(self, criteria: str = "ALL") -> List[str]:
        """搜索邮件"""
        try:
            response = self._send_command(f"SEARCH {criteria}")
            
            # 解析邮件ID列表
            lines = response.split('\n')
            for line in lines:
                if line.startswith('* SEARCH'):
                    email_ids = line.replace('* SEARCH', '').strip().split()
                    print(f"🔍 找到 {len(email_ids)} 封邮件")
                    return email_ids
            
            print("❌ 未找到邮件")
            return []
            
        except Exception as e:
            print(f"❌ 搜索邮件失败: {e}")
            return []
    
    def fetch_email_headers(self, email_id: str) -> Dict:
        """获取邮件头部信息"""
        try:
            response = self._send_command(f"FETCH {email_id} (ENVELOPE)")
            
            # 简单解析邮件头部（这里可以进一步优化）
            email_info = {
                'id': email_id,
                'subject': self._extract_field(response, 'subject'),
                'from': self._extract_field(response, 'from'),
                'date': self._extract_field(response, 'date'),
            }
            
            return email_info
            
        except Exception as e:
            print(f"❌ 获取邮件头部失败: {e}")
            return {'id': email_id, 'subject': '', 'from': '', 'date': ''}
    
    def fetch_email_body(self, email_id: str) -> str:
        """获取邮件正文"""
        try:
            response = self._send_command(f"FETCH {email_id} (BODY[TEXT])")
            
            # 提取邮件正文
            body_start = response.find('BODY[TEXT]')
            if body_start != -1:
                # 找到正文开始位置
                body_content = response[body_start:]
                # 简单提取（可以进一步优化）
                lines = body_content.split('\n')
                body_lines = []
                in_body = False
                
                for line in lines:
                    if line.startswith('{') and line.endswith('}'):
                        in_body = True
                        continue
                    if in_body and not line.startswith('A'):
                        body_lines.append(line)
                
                return '\n'.join(body_lines)
            
            return ""
            
        except Exception as e:
            print(f"❌ 获取邮件正文失败: {e}")
            return ""
    
    def _extract_field(self, response: str, field: str) -> str:
        """从ENVELOPE响应中提取字段"""
        # 这是一个简化的解析，实际ENVELOPE格式比较复杂
        # 可以根据需要进一步优化
        try:
            if field.lower() in response.lower():
                # 简单的字符串匹配提取
                pattern = rf'{field}["\s]*([^"]*)'
                match = re.search(pattern, response, re.IGNORECASE)
                if match:
                    return match.group(1).strip()
        except:
            pass
        return ""
    
    def get_recent_emails(self, limit: int = 5) -> List[Dict]:
        """获取最近的邮件"""
        if not self.select_mailbox():
            return []
        
        # 搜索所有邮件
        email_ids = self.search_emails("ALL")
        if not email_ids:
            return []
        
        # 获取最近的邮件（倒序）
        recent_ids = email_ids[-limit:] if len(email_ids) >= limit else email_ids
        recent_ids.reverse()  # 最新的在前
        
        emails = []
        for email_id in recent_ids:
            print(f"\n📧 处理邮件 {email_id}...")
            
            # 获取邮件头部
            email_info = self.fetch_email_headers(email_id)
            
            # 获取邮件正文
            email_info['body'] = self.fetch_email_body(email_id)
            
            emails.append(email_info)
        
        return emails
    
    def extract_verification_code(self, email_body: str) -> Optional[str]:
        """从邮件正文中提取验证码"""
        patterns = [
            r'验证码[：:]\s*([A-Za-z0-9]{4,8})',
            r'verification code[：:]\s*([A-Za-z0-9]{4,8})',
            r'code[：:]\s*([A-Za-z0-9]{4,8})',
            r'([A-Za-z0-9]{6})',  # 6位数字或字母
            r'([0-9]{4,8})',      # 4-8位数字
        ]
        
        for pattern in patterns:
            match = re.search(pattern, email_body, re.IGNORECASE)
            if match:
                return match.group(1)
        
        return None
    
    def wait_for_verification_email(self, sender_keyword: str = "augment", 
                                  timeout: int = 300, check_interval: int = 10) -> Optional[str]:
        """等待验证邮件并提取验证码"""
        print(f"🔍 开始等待来自 '{sender_keyword}' 的验证邮件...")
        
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            emails = self.get_recent_emails(limit=5)
            
            for email_data in emails:
                # 检查发件人是否包含关键词
                if sender_keyword.lower() in email_data['from'].lower():
                    print(f"✅ 找到验证邮件: {email_data['subject']}")
                    
                    # 提取验证码
                    verification_code = self.extract_verification_code(email_data['body'])
                    if verification_code:
                        print(f"🔑 提取到验证码: {verification_code}")
                        return verification_code
                    else:
                        print("❌ 未能从邮件中提取验证码")
                        print(f"邮件内容预览: {email_data['body'][:200]}...")
            
            print(f"⏳ 等待中... ({int(time.time() - start_time)}s/{timeout}s)")
            time.sleep(check_interval)
        
        print("⏰ 等待验证邮件超时")
        return None


def main():
    """测试socket邮件读取器"""
    print("🚀 测试基于Socket的邮件读取器")
    
    reader = SocketEmailReader()
    
    if reader.connect():
        print("\n=== 获取最近的邮件 ===")
        emails = reader.get_recent_emails(limit=3)
        
        for i, email_data in enumerate(emails, 1):
            print(f"\n--- 邮件 {i} ---")
            print(f"ID: {email_data['id']}")
            print(f"主题: {email_data['subject']}")
            print(f"发件人: {email_data['from']}")
            print(f"日期: {email_data['date']}")
            print(f"正文预览: {email_data['body'][:100]}...")
            
            # 尝试提取验证码
            code = reader.extract_verification_code(email_data['body'])
            if code:
                print(f"🔑 检测到验证码: {code}")
        
        reader.disconnect()
    else:
        print("❌ 无法连接到邮箱服务器")


if __name__ == "__main__":
    main()
