#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import poplib
import json
import socket
import email
from email.header import decode_header


def test_pop3_connection(server, port, email_addr, password, use_ssl=True):
    """测试POP3连接"""
    print(f"\n🔍 测试POP3连接: {server}:{port} (SSL: {use_ssl})")
    
    try:
        # 建立POP3连接
        if use_ssl:
            connection = poplib.POP3_SSL(server, port)
        else:
            connection = poplib.POP3(server, port)
        
        print("✅ POP3连接建立成功")
        
        # 尝试不同的用户名格式
        username_formats = [
            email_addr,  # 完整邮箱地址
            email_addr.split('@')[0],  # 仅用户名部分
        ]
        
        for username in username_formats:
            try:
                print(f"🔐 尝试用户名: {username}")
                
                # 登录
                connection.user(username)
                connection.pass_(password)
                
                print(f"✅ 登录成功！用户名: {username}")
                
                # 获取邮件统计
                num_messages, total_size = connection.stat()
                print(f"📧 邮箱统计: {num_messages} 封邮件, 总大小: {total_size} 字节")
                
                # 获取最近几封邮件的信息
                if num_messages > 0:
                    print("📋 最近的邮件:")
                    for i in range(min(3, num_messages)):
                        msg_num = num_messages - i  # 从最新的开始
                        msg_info = connection.list(msg_num)
                        print(f"  邮件 {msg_num}: {msg_info}")
                        
                        # 获取邮件头部
                        try:
                            header_lines = connection.top(msg_num, 0)
                            header_text = b'\n'.join(header_lines[1]).decode('utf-8', errors='ignore')
                            msg = email.message_from_string(header_text)
                            
                            subject = decode_header(msg['Subject'])[0][0] if msg['Subject'] else "无主题"
                            if isinstance(subject, bytes):
                                subject = subject.decode('utf-8', errors='ignore')
                            
                            sender = msg['From'] or "未知发件人"
                            date = msg['Date'] or "未知日期"
                            
                            print(f"    主题: {subject}")
                            print(f"    发件人: {sender}")
                            print(f"    日期: {date}")
                        except Exception as e:
                            print(f"    获取邮件头部失败: {e}")
                
                connection.quit()
                return True, username
                
            except poplib.error_proto as e:
                print(f"❌ 用户名 {username} 登录失败: {e}")
                continue
        
        print("❌ 所有用户名格式都登录失败")
        connection.quit()
        return False, None
        
    except Exception as e:
        print(f"❌ POP3连接错误: {e}")
        return False, None


def main():
    """测试POP3连接"""
    print("🚀 开始测试2925邮箱POP3连接")
    
    # 读取配置
    with open('config.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    email_addr = config['email']['address']
    password = config['email']['password']
    
    print(f"📧 测试邮箱: {email_addr}")
    print("=" * 60)
    
    # POP3配置列表
    pop3_configs = [
        {'name': 'POP3 SSL 995', 'server': 'mail.2925.com', 'port': 995, 'ssl': True},
        {'name': 'POP3 SSL 995 (pop)', 'server': 'pop.2925.com', 'port': 995, 'ssl': True},
        {'name': 'POP3 标准 110', 'server': 'mail.2925.com', 'port': 110, 'ssl': False},
        {'name': 'POP3 标准 110 (pop)', 'server': 'pop.2925.com', 'port': 110, 'ssl': False},
        {'name': 'POP3 SSL 995 (2925)', 'server': '2925.com', 'port': 995, 'ssl': True},
        {'name': 'POP3 标准 110 (2925)', 'server': '2925.com', 'port': 110, 'ssl': False},
    ]
    
    success_configs = []
    
    for config_info in pop3_configs:
        try:
            success, username = test_pop3_connection(
                config_info['server'],
                config_info['port'],
                email_addr,
                password,
                config_info['ssl']
            )
            
            if success:
                config_info['working_username'] = username
                success_configs.append(config_info)
                print(f"🎉 {config_info['name']} 连接成功！")
            else:
                print(f"💥 {config_info['name']} 连接失败")
                
        except KeyboardInterrupt:
            print("\n⏹️ 用户中断测试")
            break
        except Exception as e:
            print(f"💥 {config_info['name']} 测试异常: {e}")
        
        print("-" * 60)
    
    # 总结
    print("\n📊 测试总结:")
    if success_configs:
        print("✅ 成功的POP3配置:")
        for config_info in success_configs:
            print(f"  - {config_info['name']}: {config_info['server']}:{config_info['port']} (SSL: {config_info['ssl']})")
            print(f"    用户名: {config_info['working_username']}")
        
        # 更新配置文件建议
        best_config = success_configs[0]
        print(f"\n💡 建议更新config.json使用以下配置:")
        print(f"  服务器: {best_config['server']}")
        print(f"  端口: {best_config['port']}")
        print(f"  SSL: {best_config['ssl']}")
        print(f"  用户名: {best_config['working_username']}")
        
    else:
        print("❌ 没有找到可用的POP3配置")
        print("\n🔧 建议:")
        print("  1. 检查邮箱地址和密码是否正确")
        print("  2. 联系2925邮箱服务商确认邮件协议支持")
        print("  3. 检查是否需要在邮箱设置中启用POP3/IMAP访问")


if __name__ == "__main__":
    main()
