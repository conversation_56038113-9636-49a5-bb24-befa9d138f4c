#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import socket
import json


def test_login_with_config():
    """使用配置文件中的密码测试登录"""
    print("🔍 使用配置文件密码测试登录...")
    
    # 读取配置
    with open('config.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    email = config['email']['address']
    password = config['email']['password']
    
    print(f"📧 邮箱: {email}")
    print(f"🔑 密码: {password}")
    
    try:
        # 创建socket连接
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        sock.connect(('imap.2925.com', 143))
        
        # 接收欢迎消息
        welcome = sock.recv(1024).decode()
        print(f"📨 服务器欢迎: {welcome.strip()}")
        
        # 尝试登录
        login_cmd = f'A001 LOGIN {email} {password}\r\n'
        print(f"📤 发送登录命令: {login_cmd.strip()}")
        
        sock.send(login_cmd.encode())
        login_response = sock.recv(1024).decode()
        print(f"📥 登录响应: {login_response.strip()}")
        
        if 'OK' in login_response:
            print("🎉 登录成功！")
            
            # 测试一个简单的命令
            sock.send(b'A002 LIST "" "*"\r\n')
            list_response = sock.recv(4096).decode()
            print(f"📋 邮箱列表: {list_response.strip()}")
            
            # 登出
            sock.send(b'A003 LOGOUT\r\n')
            sock.recv(1024)
            sock.close()
            return True
        else:
            print("❌ 登录失败")
            sock.close()
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_different_passwords():
    """测试不同的密码组合"""
    print("\n🔍 测试不同的密码组合...")
    
    passwords = [
        'edward2wong',
        'edward22wong', 
        'Edward2wong',
        'Edward22wong',
        'EDWARD2WONG',
        'EDWARD22WONG'
    ]
    
    for password in passwords:
        print(f"\n🔑 测试密码: {password}")
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            sock.connect(('imap.2925.com', 143))
            
            # 接收欢迎消息
            welcome = sock.recv(1024).decode()
            
            # 尝试登录
            login_cmd = f'A001 LOGIN <EMAIL> {password}\r\n'
            sock.send(login_cmd.encode())
            login_response = sock.recv(1024).decode()
            
            if 'OK' in login_response:
                print(f"    ✅ 成功: {password}")
                sock.send(b'A002 LOGOUT\r\n')
                sock.recv(1024)
                sock.close()
                return password
            else:
                print(f"    ❌ 失败: {login_response.strip()}")
            
            sock.close()
            
        except Exception as e:
            print(f"    ❌ 异常: {e}")
    
    return None


def main():
    """主测试函数"""
    print("🚀 简单登录测试")
    print("=" * 50)
    
    # 测试1: 使用配置文件密码
    if test_login_with_config():
        print("✅ 配置文件密码正确")
        return
    
    # 测试2: 尝试不同密码
    correct_password = test_different_passwords()
    if correct_password:
        print(f"\n🎉 找到正确密码: {correct_password}")
        
        # 更新配置文件
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        config['email']['password'] = correct_password
        
        with open('config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print("✅ 配置文件已更新")
    else:
        print("\n❌ 没有找到正确的密码")


if __name__ == "__main__":
    main()
